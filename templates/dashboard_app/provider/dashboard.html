{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Provider Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Provider Dashboard{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
{{ block.super }}
<!-- Additional Google Fonts for Display Typography -->
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* CozyWish Provider Dashboard - Professional Dashboard Design System */

    /* CSS Custom Properties */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Dashboard Specific Colors */
        --cw-dashboard-bg: #f8fafc;
        --cw-dashboard-sidebar: #ffffff;
        --cw-dashboard-content: #ffffff;
        --cw-dashboard-border: #e2e8f0;
        --cw-dashboard-card: #ffffff;
        --cw-dashboard-hover: #f1f5f9;

        /* Secondary Colors */
        --cw-secondary-50: #f9f7f4;
        --cw-secondary-100: #f1ebe2;
        --cw-secondary-200: #e3d5c4;
        --cw-secondary-300: #d1b89e;
        --cw-secondary-400: #bc9876;
        --cw-secondary-500: #ad7f5a;
        --cw-secondary-600: #a0704e;
        --cw-secondary-700: #855a42;
        --cw-secondary-800: #6c4a39;
        --cw-secondary-900: #583d30;
        --cw-secondary-950: #2f1f18;

        /* Neutral Colors */
        --cw-neutral-50: #fafafa;
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-300: #d4d4d4;
        --cw-neutral-400: #a3a3a3;
        --cw-neutral-500: #737373;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-900: #171717;
        --cw-neutral-950: #0a0a0a;

        /* Semantic Colors */
        --cw-success: #059669;
        --cw-warning: #d97706;
        --cw-error: #dc2626;
        --cw-info: #0284c7;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --cw-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

        /* Dashboard Gradients */
        --cw-gradient-dashboard: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
        --cw-gradient-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-accent: linear-gradient(135deg, var(--cw-brand-accent) 0%, var(--cw-accent-dark) 100%);
    }

    /* Dashboard Global Styles */
    .dashboard-wrapper {
        background: var(--cw-dashboard-bg) !important;
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        min-height: 100vh;
    }

    /* Typography */
    .dashboard-wrapper h1, .dashboard-wrapper h2, .dashboard-wrapper h3,
    .dashboard-wrapper h4, .dashboard-wrapper h5, .dashboard-wrapper h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    .display-font {
        font-family: var(--cw-font-display);
    }

    /* Brand Colors */
    .text-brand-cw { color: var(--cw-brand-primary) !important; }
    .text-brand-light-cw { color: var(--cw-brand-light) !important; }
    .text-accent-cw { color: var(--cw-brand-accent) !important; }
    .text-secondary-cw { color: var(--cw-secondary-950) !important; }
    .text-neutral-cw { color: var(--cw-neutral-600) !important; }
    .bg-brand-cw { background-color: var(--cw-brand-primary) !important; }
    .bg-brand-accent-cw { background-color: var(--cw-brand-accent) !important; }
    .bg-accent-dark-cw { background-color: var(--cw-accent-dark) !important; }
    .bg-light-cw { background-color: var(--cw-accent-light) !important; }

    /* Dashboard Layout */
    .dashboard-content {
        background: var(--cw-dashboard-content) !important;
        padding: 2rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    /* Dashboard Header */
    .dashboard-header {
        background: var(--cw-dashboard-card);
        border: 1px solid var(--cw-dashboard-border);
        border-radius: 0.75rem;
        padding: 1.5rem 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .dashboard-header h1 {
        font-size: 1.875rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .dashboard-header .subtitle {
        color: var(--cw-neutral-600);
        font-size: 1rem;
        margin: 0;
    }

    /* Stats Grid - Modern Dashboard Style */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2.5rem;
    }

    .stat-item {
        background: var(--cw-dashboard-card);
        border: 1px solid var(--cw-dashboard-border);
        border-radius: 0.75rem;
        padding: 1.5rem;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        position: relative;
        overflow: hidden;
    }

    .stat-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-accent);
    }

    .stat-item.featured {
        background: var(--cw-gradient-brand-button);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    .stat-item.featured::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 20px 20px 0;
        border-color: transparent rgba(255,255,255,0.1) transparent transparent;
    }

    .stat-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    .stat-item.featured .stat-icon {
        background: rgba(255,255,255,0.2);
        color: white;
    }

    .stat-number {
        font-family: var(--cw-font-heading);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.25rem;
        line-height: 1;
    }

    .stat-item.featured .stat-number {
        color: white;
    }

    .stat-label {
        font-family: var(--cw-font-primary);
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--cw-neutral-600);
        margin: 0;
    }

    .stat-item.featured .stat-label {
        color: rgba(255,255,255,0.9);
    }

    .stat-change {
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        background: var(--cw-success);
        color: white;
    }

    .stat-change.negative {
        background: var(--cw-error);
    }

    /* Content Sections - Dashboard Style */
    .content-section {
        margin-bottom: 2rem;
    }

    .section-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 0.5rem;
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
    }

    .section-content {
        background: var(--cw-dashboard-card);
        border: 1px solid var(--cw-dashboard-border);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: var(--cw-shadow-sm);
        transition: all 0.2s ease;
    }

    .section-content:hover {
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-accent);
    }

    /* Actions Grid - Dashboard Style */
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .action-btn {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 1rem 1.25rem;
        background: var(--cw-dashboard-card);
        border: 1px solid var(--cw-dashboard-border);
        border-radius: 0.5rem;
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
        font-family: var(--cw-font-primary);
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-size: 0.875rem;
    }

    .action-btn:hover {
        background: var(--cw-dashboard-hover);
        border-color: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    .action-btn.primary {
        background: var(--cw-gradient-brand-button);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    .action-btn.primary:hover {
        background: var(--cw-brand-light);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    .action-btn i {
        font-size: 1rem;
        margin-right: 0.5rem;
        width: 16px;
        text-align: center;
    }

    /* Booking Items - Dashboard Style */
    .booking-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        margin-bottom: 0.75rem;
        background: var(--cw-dashboard-card);
        border: 1px solid var(--cw-dashboard-border);
        border-radius: 0.5rem;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .booking-item:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-accent);
        background: var(--cw-dashboard-hover);
    }

    .booking-item:last-child {
        margin-bottom: 0;
    }

    .booking-info {
        flex: 1;
    }

    .booking-time {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    /* Dashboard Specific Components */
    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        color: var(--cw-neutral-600);
    }

    .empty-state-icon {
        font-size: 3rem;
        color: var(--cw-neutral-400);
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: var(--cw-neutral-700);
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        margin-bottom: 1.5rem;
    }

    /* Welcome Section Styling */
    .welcome-content {
        max-width: 600px;
        margin: 0 auto;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-content {
            padding: 1rem;
        }

        .dashboard-header {
            padding: 1rem;
        }

        .dashboard-header h1 {
            font-size: 1.5rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .stat-item {
            padding: 1rem;
        }

        .stat-number {
            font-size: 1.5rem;
        }

        .actions-grid {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .section-content {
            padding: 1rem;
        }
    }

    @media (max-width: 576px) {
        .stats-grid {
            grid-template-columns: 1fr 1fr;
        }

        .stat-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .stat-change {
            align-self: flex-end;
        }
    }

    .booking-customer {
        color: var(--cw-neutral-700);
        font-size: 1rem;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .booking-service {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
    }

    .booking-actions {
        display: flex;
        gap: 0.75rem;
        align-items: center;
    }

    .booking-status {
        padding: 0.5rem 1rem;
        border-radius: 1.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-family: var(--cw-font-heading);
    }

    .booking-status.pending {
        background: rgba(245, 158, 11, 0.15);
        color: var(--cw-warning);
        border: 1px solid rgba(245, 158, 11, 0.3);
    }

    .booking-status.confirmed {
        background: rgba(5, 150, 105, 0.15);
        color: var(--cw-success);
        border: 1px solid rgba(5, 150, 105, 0.3);
    }

    .booking-status.completed {
        background: rgba(2, 132, 199, 0.15);
        color: var(--cw-info);
        border: 1px solid rgba(2, 132, 199, 0.3);
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--cw-neutral-600);
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        border: 1px solid rgba(250, 225, 215, 0.3);
    }

    .empty-state-icon {
        font-size: 4rem;
        color: var(--cw-brand-accent);
        margin-bottom: 1.5rem;
    }

    .empty-state h5 {
        font-family: var(--cw-font-heading);
        color: var(--cw-brand-primary);
        font-weight: 600;
        margin-bottom: 1rem;
        font-size: 1.375rem;
    }

    .empty-state p {
        margin-bottom: 2rem;
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Approval Status Styling */
    .approval-status-card {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: var(--cw-shadow-md);
    }

    .status-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1.5rem;
    }

    .status-badge {
        padding: 0.75rem 1.5rem;
        border-radius: 1.5rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        display: inline-flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .status-badge.pending {
        background: rgba(245, 158, 11, 0.15);
        color: var(--cw-warning);
        border: 1px solid rgba(245, 158, 11, 0.3);
    }

    .status-badge.rejected {
        background: rgba(220, 38, 38, 0.15);
        color: var(--cw-error);
        border: 1px solid rgba(220, 38, 38, 0.3);
    }

    .status-badge.draft {
        background: rgba(115, 115, 115, 0.15);
        color: var(--cw-neutral-600);
        border: 1px solid rgba(115, 115, 115, 0.3);
    }

    /* Welcome Section Styles */
    .welcome-hero {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        padding: 3rem 2rem;
        box-shadow: var(--cw-shadow-md);
        margin-bottom: 2rem;
    }

    .welcome-content h1 {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
    }

    .welcome-actions {
        margin-top: 2rem;
    }

    /* Onboarding Checklist Widget Styles */
    .checklist-widget-item {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 0.75rem;
        padding: 1.5rem;
        transition: all 0.3s ease;
        height: 100%;
        border-left: 4px solid var(--cw-neutral-300);
    }

    .checklist-widget-item.completed {
        border-left-color: var(--cw-success);
        background: linear-gradient(135deg, #ffffff 0%, rgba(5, 150, 105, 0.05) 100%);
    }

    .checklist-widget-item.high-priority {
        border-left-color: var(--cw-error);
        background: linear-gradient(135deg, #ffffff 0%, rgba(220, 38, 38, 0.05) 100%);
    }

    .checklist-widget-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-accent);
    }

    .checklist-icon {
        width: 40px;
        text-align: center;
    }

    /* Feature Preview Widget Styles */
    .feature-preview-card {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        padding: 2rem 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-md);
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .feature-preview-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .feature-icon {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
    }

    .feature-title {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-size: 1.25rem;
    }

    .feature-description {
        color: var(--cw-neutral-600);
        margin-bottom: 1.5rem;
        flex-grow: 1;
    }

    .feature-benefits {
        list-style: none;
        padding: 0;
        margin: 0;
        text-align: left;
    }

    .feature-benefits li {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        color: var(--cw-neutral-700);
    }

    /* Help Resources Widget Styles */
    .help-resource-item {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 0.75rem;
        padding: 1.25rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .help-resource-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-accent);
    }

    .help-resource-item:last-child {
        margin-bottom: 0;
    }

    .help-icon {
        width: 40px;
        text-align: center;
    }

    /* Utility Classes */
    .shadow-cw-sm { box-shadow: var(--cw-shadow-sm); }
    .shadow-cw-md { box-shadow: var(--cw-shadow-md); }
    .shadow-cw-lg { box-shadow: var(--cw-shadow-lg); }

    .rounded-cw { border-radius: 0.5rem; }
    .rounded-cw-lg { border-radius: 1rem; }
    .rounded-cw-full { border-radius: 9999px; }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-content {
            padding: 1.5rem 1rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
        }

        .stat-item {
            padding: 1.5rem 1rem;
        }

        .stat-number {
            font-size: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
        }

        .section-content {
            padding: 1.5rem;
        }

        .booking-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.25rem;
        }

        .booking-actions {
            width: 100%;
            justify-content: flex-end;
        }

        .actions-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .action-btn {
            padding: 1.25rem 1.5rem;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            padding: 0.875rem 1.5rem;
            font-size: 0.95rem;
        }

        .status-header {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block dashboard_actions %}
<div class="d-flex flex-wrap align-items-center gap-3">
    {% if not venue %}
    <a href="{% url 'venues_app:venue_create' %}" class="btn-cw-primary" data-bs-toggle="tooltip" title="Add Venue">
        <i class="fas fa-plus me-2"></i>Add Venue
    </a>
    {% endif %}
    {% if venue %}
        {% if venue.approval_status == 'draft' and can_submit_for_approval %}
        <button type="button" class="btn-cw-primary" data-bs-toggle="modal" data-bs-target="#approvalSubmissionModal" data-bs-toggle="tooltip" title="Submit for Approval">
            <i class="fas fa-paper-plane me-2"></i>Submit for Approval
        </button>
        {% elif venue.approval_status == 'draft' %}
        <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#approvalRequirementsModal" data-bs-toggle="tooltip" title="View Requirements">
            <i class="fas fa-exclamation-triangle me-2"></i>Complete Profile
        </button>
        {% endif %}
        <a href="{% url 'venues_app:manage_services' %}" class="btn-cw-secondary" data-bs-toggle="tooltip" title="Manage Services">
            <i class="fas fa-spa me-2"></i>Manage Services
        </a>
    {% endif %}
</div>
{% endblock %}

{% block dashboard_content %}
<!-- Dashboard Header -->
<div class="dashboard-header">
    <h1>
        {% if provider_profile.business_name %}
            {{ provider_profile.business_name }} Dashboard
        {% else %}
            Provider Dashboard
        {% endif %}
    </h1>
    <p class="subtitle">
        {% if not has_venue %}
            Welcome to CozyWish! Complete your setup to start receiving bookings.
        {% else %}
            Manage your business, track performance, and grow your customer base.
        {% endif %}
    </p>
</div>

<!-- Welcome Section for Providers Without Venues -->
{% if not has_venue %}
<div class="content-section">
    <div class="section-content">
        <div class="text-center">
            <div class="mb-4">
                <i class="fas fa-spa text-brand-cw" style="font-size: 3rem;"></i>
            </div>
            <h3 class="text-brand-cw mb-3">
                Welcome to CozyWish,
                {% if provider_profile.business_name %}
                    {{ provider_profile.business_name }}!
                {% else %}
                    {{ provider_profile.user.get_full_name|default:provider_profile.user.username }}!
                {% endif %}
            </h3>
            <p class="text-neutral-cw mb-4">
                You're just a few steps away from connecting with customers and growing your business.
                Let's get you set up for success!
            </p>

            {% if onboarding_progress %}
            <div class="row justify-content-center mb-4">
                <div class="col-md-6">
                    <div class="progress mb-3" style="height: 20px; background-color: var(--cw-neutral-200);">
                        <div class="progress-bar"
                             style="width: {{ onboarding_progress.progress_percentage }}%; background: var(--cw-gradient-brand-button);"
                             role="progressbar"
                             aria-valuenow="{{ onboarding_progress.progress_percentage }}"
                             aria-valuemin="0" aria-valuemax="100">
                            {{ onboarding_progress.completed_steps }} of {{ onboarding_progress.total_steps }} completed
                        </div>
                    </div>
                    <p class="mb-0 text-neutral-cw">
                        <strong>{{ onboarding_progress.completed_steps }}</strong> out of <strong>{{ onboarding_progress.total_steps }}</strong> steps completed
                    </p>
                </div>
            </div>
            {% endif %}

            <div class="actions-grid mt-4">
                <a href="{% url 'venues_app:venue_create' %}" class="action-btn primary">
                    <i class="fas fa-plus-circle"></i>
                    Create Your Venue
                </a>
                <a href="{% url 'accounts_app:service_provider_profile_edit' %}" class="action-btn">
                    <i class="fas fa-user-edit"></i>
                    Complete Profile
                </a>
                <a href="{% url 'dashboard_app:provider_team_management' %}" class="action-btn">
                    <i class="fas fa-users"></i>
                    Add Team Members
                </a>
                <a href="#onboarding-checklist" class="action-btn">
                    <i class="fas fa-list-check"></i>
                    View Checklist
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Venue Approval Status -->
{% if venue and venue.approval_status != 'approved' %}
<div class="content-section">
    <h2 class="section-title">
        <i class="fas fa-gavel me-2"></i>
        Venue Approval Status
    </h2>
    <div class="section-content">
        <div class="approval-status-card">
            <div class="status-header">
                <div class="status-info">
                    <div class="status-badge {% if venue.approval_status == 'pending' %}pending{% elif venue.approval_status == 'rejected' %}rejected{% else %}draft{% endif %}">
                        <i class="fas {% if venue.approval_status == 'pending' %}fa-clock{% elif venue.approval_status == 'rejected' %}fa-times-circle{% else %}fa-edit{% endif %} me-2"></i>
                        {{ venue.get_approval_status_display }}
                    </div>
                    {% if approval_progress %}
                    <div class="completion-progress">
                        <div class="progress-text">
                            <span class="progress-label">Profile Completion</span>
                            <span class="progress-percentage">{{ approval_progress.completion_percentage }}%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar" style="width: {{ approval_progress.completion_percentage }}%"></div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% if can_submit_for_approval %}
                <button type="button" class="btn-cw-primary" data-bs-toggle="modal" data-bs-target="#approvalSubmissionModal">
                    <i class="fas fa-paper-plane me-2"></i>Submit for Approval
                </button>
                {% elif venue.approval_status == 'draft' %}
                <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#approvalRequirementsModal">
                    <i class="fas fa-list-check me-2"></i>View Requirements
                </button>
                {% endif %}
            </div>

            {% if approval_progress and not can_submit_for_approval %}
            <div class="requirements-summary">
                <h6 class="requirements-title">Missing Requirements:</h6>
                <div class="requirements-list">
                    {% for req in approval_progress.high_priority_missing|slice:":3" %}
                    <div class="requirement-item high-priority">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>{{ req.requirement }}: {{ req.description }}</span>
                    </div>
                    {% endfor %}
                    {% if approval_progress.high_priority_missing|length > 3 %}
                    <div class="requirement-item">
                        <i class="fas fa-ellipsis-h"></i>
                        <span>And {{ approval_progress.high_priority_missing|length|add:"-3" }} more requirements...</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

<!-- Stats Overview -->
<div class="stats-grid">
    <div class="stat-item featured">
        <div class="stat-header">
            <div class="stat-icon">
                <i class="fas fa-calendar-day"></i>
            </div>
            <div class="stat-change">+12%</div>
        </div>
        <div class="stat-number">{{ todays_bookings_count|default:0 }}</div>
        <div class="stat-label">Today's Bookings</div>
    </div>

    <div class="stat-item">
        <div class="stat-header">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
        <div class="stat-number">{{ total_bookings|default:0 }}</div>
        <div class="stat-label">Total Bookings</div>
    </div>

    <div class="stat-item">
        <div class="stat-header">
            <div class="stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
        </div>
        <div class="stat-number">${{ monthly_earnings|floatformat:0|default:0 }}</div>
        <div class="stat-label">Monthly Earnings</div>
    </div>

    <div class="stat-item">
        <div class="stat-header">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
        </div>
        <div class="stat-number">{{ pending_bookings|default:0 }}</div>
        <div class="stat-label">Pending Bookings</div>
    </div>
</div>

<!-- Onboarding Checklist Widget (only shown when no venue exists) -->
{% if not has_venue and onboarding_checklist %}
<div class="content-section" id="onboarding-checklist">
    <h2 class="section-title">
        <i class="fas fa-clipboard-check me-2"></i>
        Getting Started Checklist
    </h2>
    <div class="section-content">
        <p class="text-neutral-cw mb-4">
            Complete these steps to unlock all features and start accepting bookings from customers.
        </p>

        <div class="row">
            {% for item in onboarding_checklist %}
            <div class="col-md-6 mb-3">
                <div class="checklist-widget-item {% if item.completed %}completed{% elif item.priority == 'high' %}high-priority{% endif %}">
                    <div class="d-flex align-items-center">
                        <div class="checklist-icon me-3">
                            {% if item.completed %}
                                <i class="{{ item.icon }} text-success fa-lg"></i>
                            {% else %}
                                <i class="{{ item.icon }} text-muted fa-lg"></i>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">
                                {{ item.step }}
                                {% if item.completed %}
                                    <span class="badge bg-success ms-2">
                                        <i class="fas fa-check"></i> Done
                                    </span>
                                {% elif item.priority == 'high' %}
                                    <span class="badge bg-danger ms-2">Required</span>
                                {% else %}
                                    <span class="badge bg-secondary ms-2">Optional</span>
                                {% endif %}
                            </h6>
                            <p class="text-muted mb-2 small">{{ item.description }}</p>
                            {% if not item.completed and item.url %}
                                <a href="{% url item.url %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-arrow-right"></i> Start
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Feature Preview Widget (only shown when no venue exists) -->
{% if not has_venue and feature_preview %}
<div class="content-section">
    <h2 class="section-title">
        <i class="fas fa-star me-2"></i>
        What You'll Unlock
    </h2>
    <div class="section-content">
        <p class="text-neutral-cw mb-4">
            Here's what you'll be able to do once you complete your venue setup:
        </p>

        <div class="row">
            {% for feature in feature_preview %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="feature-preview-card">
                    <div class="feature-icon text-{{ feature.color }}">
                        <i class="{{ feature.icon }}"></i>
                    </div>
                    <h5 class="feature-title">{{ feature.title }}</h5>
                    <p class="feature-description">{{ feature.description }}</p>
                    <ul class="feature-benefits">
                        {% for benefit in feature.benefits %}
                        <li>
                            <i class="fas fa-check text-success me-2"></i>{{ benefit }}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Actions -->
<div class="content-section">
    <h2 class="section-title">
        <i class="fas fa-bolt"></i>
        Quick Actions
    </h2>
    <div class="section-content">
        <div class="actions-grid">
            {% if not has_venue %}
            <a href="{% url 'venues_app:venue_create' %}" class="action-btn primary">
                <i class="fas fa-plus"></i>
                Create Your Venue
            </a>
            {% if help_resources.quick_actions %}
                {% for action in help_resources.quick_actions %}
                {% if action.url|slice:":4" == "http" %}
                    <a href="{{ action.url }}" class="action-btn" target="_blank">
                        <i class="{{ action.icon }}"></i>
                        {{ action.title }}
                    </a>
                {% else %}
                    <a href="{% url action.url %}" class="action-btn">
                        <i class="{{ action.icon }}"></i>
                        {{ action.title }}
                    </a>
                {% endif %}
                {% endfor %}
            {% endif %}
            {% else %}
            <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="action-btn">
                <i class="fas fa-calendar-day"></i>
                Today's Schedule
            </a>
            <a href="{% url 'dashboard_app:provider_earnings_reports' %}" class="action-btn">
                <i class="fas fa-chart-line"></i>
                Earnings Report
            </a>
            <a href="{% url 'venues_app:venue_manage' %}" class="action-btn">
                <i class="fas fa-cog"></i>
                Venue Settings
            </a>
            {% endif %}
            <a href="{% url 'dashboard_app:provider_team_management' %}" class="action-btn">
                <i class="fas fa-users"></i>
                Team Management
            </a>
        </div>
    </div>
</div>

<!-- Help Resources Widget (only shown when no venue exists) -->
{% if not has_venue and help_resources %}
<div class="content-section">
    <div class="row">
        <!-- Help & Guides -->
        <div class="col-md-6">
            <h3 class="section-title">
                <i class="fas fa-question-circle me-2"></i>
                Help & Guides
            </h3>
            <div class="section-content">
                {% for guide in help_resources.guides %}
                <div class="help-resource-item">
                    <div class="d-flex align-items-center">
                        <div class="help-icon me-3">
                            <i class="{{ guide.icon }} text-primary fa-lg"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ guide.title }}</h6>
                            <p class="text-muted mb-0 small">{{ guide.description }}</p>
                        </div>
                        <div>
                            <a href="{{ guide.url }}" class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Support -->
        <div class="col-md-6">
            <h3 class="section-title">
                <i class="fas fa-headset me-2"></i>
                Support
            </h3>
            <div class="section-content">
                {% for support in help_resources.support %}
                <div class="help-resource-item">
                    <div class="d-flex align-items-center">
                        <div class="help-icon me-3">
                            <i class="{{ support.icon }} text-success fa-lg"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ support.title }}</h6>
                            <p class="text-muted mb-0 small">{{ support.description }}</p>
                        </div>
                        <div>
                            <a href="{{ support.url }}" class="btn btn-outline-success btn-sm" target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Today's Bookings -->
<div class="content-section">
    <h2 class="section-title">
        <i class="fas fa-calendar-day me-2"></i>
        Today's Bookings
    </h2>
    {% if todays_bookings %}
    <div class="section-content">
        {% for booking in todays_bookings %}
        <div class="booking-item">
            <div class="booking-info">
                <div class="booking-time">
                    {% for item in booking.items.all %}
                        {{ item.scheduled_time|time:"g:i A" }}{% if not forloop.last %}, {% endif %}
                    {% endfor %}
                </div>
                <div class="booking-customer">
                    <i class="fas fa-user me-1"></i>
                    {{ booking.customer.get_full_name }}
                </div>
                <div class="booking-service">
                    {% for item in booking.items.all %}
                        {{ item.service.service_title }}{% if not forloop.last %}, {% endif %}
                    {% endfor %}
                </div>
            </div>
            <div class="booking-actions">
                <span class="booking-status {{ booking.status }}">{{ booking.get_status_display }}</span>
                <a href="{% url 'booking_cart_app:provider_booking_detail' booking.id %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-eye"></i>
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-calendar-times"></i>
        </div>
        <h5>No bookings for today</h5>
        <p>Your schedule is clear for today.</p>
        <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn-cw-secondary">
            <i class="fas fa-calendar me-2"></i>View All Bookings
        </a>
    </div>
    {% endif %}
</div>

<!-- Performance Summary -->
<div class="content-section">
    <h2 class="section-title">
        <i class="fas fa-chart-bar"></i>
        Performance Summary
    </h2>
    <div class="section-content">
        <div class="row g-3">
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: rgba(5, 150, 105, 0.1); color: var(--cw-success);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-number" style="color: var(--cw-success); font-size: 1.5rem;">{{ confirmed_bookings|default:0 }}</div>
                    <div class="stat-label">Confirmed</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: rgba(217, 119, 6, 0.1); color: var(--cw-warning);">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-number" style="color: var(--cw-warning); font-size: 1.5rem;">{{ pending_bookings|default:0 }}</div>
                    <div class="stat-label">Pending</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: rgba(2, 132, 199, 0.1); color: var(--cw-info);">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                    </div>
                    <div class="stat-number" style="color: var(--cw-info); font-size: 1.5rem;">{{ completed_bookings|default:0 }}</div>
                    <div class="stat-label">Completed</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="stat-number" style="color: var(--cw-brand-primary); font-size: 1.5rem;">${{ total_earnings|floatformat:0|default:0 }}</div>
                    <div class="stat-label">Total Earned</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- Approval Submission Modal -->
<div class="modal fade" id="approvalSubmissionModal" tabindex="-1" aria-labelledby="approvalSubmissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalSubmissionModalLabel">
                    <i class="fas fa-paper-plane me-2"></i>Submit Venue for Approval
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="approval-confirmation">
                    <div class="confirmation-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h6>Ready for Submission!</h6>
                    <p>Your venue meets all requirements and is ready to be submitted for approval. Once submitted, our team will review your venue and notify you of the decision.</p>

                    <div class="submission-details">
                        <h6>What happens next:</h6>
                        <ul>
                            <li>Your venue will be reviewed within 24-48 hours</li>
                            <li>You'll receive an email notification with the decision</li>
                            <li>If approved, your venue will be visible to customers</li>
                            <li>If changes are needed, you'll receive specific feedback</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-cw-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="{% url 'dashboard_app:provider_venue_approval_submission' %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn-cw-primary">
                        <i class="fas fa-paper-plane me-2"></i>Submit for Approval
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Approval Requirements Modal -->
<div class="modal fade" id="approvalRequirementsModal" tabindex="-1" aria-labelledby="approvalRequirementsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalRequirementsModalLabel">
                    <i class="fas fa-list-check me-2"></i>Approval Requirements
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {% if approval_progress %}
                <div class="requirements-overview">
                    <div class="progress-summary">
                        <div class="progress-circle-small">
                            <span class="progress-percentage">{{ approval_progress.completion_percentage }}%</span>
                        </div>
                        <div class="progress-info">
                            <h6>Profile Completion</h6>
                            <p>{{ approval_progress.completed_criteria }} of {{ approval_progress.total_criteria }} requirements completed</p>
                        </div>
                    </div>

                    {% if approval_progress.high_priority_missing %}
                    <div class="requirements-section">
                        <h6 class="requirements-section-title high-priority">
                            <i class="fas fa-exclamation-triangle"></i>High Priority Requirements
                        </h6>
                        {% for req in approval_progress.high_priority_missing %}
                        <div class="requirement-card high-priority">
                            <div class="requirement-info">
                                <h6>{{ req.requirement }}</h6>
                                <p>{{ req.description }}</p>
                                <div class="requirement-status">
                                    <span class="current">Current: {{ req.current }}</span>
                                    <span class="required">Required: {{ req.required }}</span>
                                </div>
                            </div>
                            <div class="requirement-action">
                                <a href="{% url req.action_url %}" class="btn btn-sm btn-outline-danger">Fix Now</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    {% if approval_progress.medium_priority_missing %}
                    <div class="requirements-section">
                        <h6 class="requirements-section-title medium-priority">
                            <i class="fas fa-info-circle"></i>Medium Priority Requirements
                        </h6>
                        {% for req in approval_progress.medium_priority_missing %}
                        <div class="requirement-card medium-priority">
                            <div class="requirement-info">
                                <h6>{{ req.requirement }}</h6>
                                <p>{{ req.description }}</p>
                                <div class="requirement-status">
                                    <span class="current">Current: {{ req.current }}</span>
                                    <span class="required">Required: {{ req.required }}</span>
                                </div>
                            </div>
                            <div class="requirement-action">
                                <a href="{% url req.action_url %}" class="btn btn-sm btn-outline-warning">Complete</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-cw-secondary" data-bs-dismiss="modal">Close</button>
                <a href="{% url 'venues_app:venue_edit' %}" class="btn-cw-primary">
                    <i class="fas fa-edit me-2"></i>Edit Venue
                </a>
            </div>
        </div>
    </div>
</div>

{% block dashboard_extra_js %}
<script>
// Enhanced Provider Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeProviderDashboard();
});

function initializeProviderDashboard() {
    // Add loading states to action buttons
    setupActionButtons();
    
    // Initialize any real-time updates
    setupRealTimeUpdates();
}

function setupActionButtons() {
    const actionButtons = document.querySelectorAll('.action-btn');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add loading state to button
            const icon = this.querySelector('i');
            if (icon) {
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin me-2';
                
                // Reset after navigation
                setTimeout(() => {
                    icon.className = originalClass;
                }, 2000);
            }
        });
    });
}

function setupRealTimeUpdates() {
    // Placeholder for real-time updates functionality
    // This can be enhanced with WebSocket connections or periodic AJAX calls
    console.log('Provider dashboard real-time updates initialized');
}
</script>
{% endblock %}
