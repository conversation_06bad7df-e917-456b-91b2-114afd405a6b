# """URL configuration for the dashboard application."""

# --- Third-Party Imports ---
from django.urls import path

# --- Local App Imports ---
from . import views


app_name = 'dashboard_app'


# --- URL Patterns ---
urlpatterns = [
    # Monitoring
    path('health/', views.health_check, name='health_check'),
    # Customer Dashboard URLs
    path('customer/', views.customer_dashboard_view, name='customer_dashboard'),
    path('customer/booking-status/', views.customer_booking_status_view, name='customer_booking_status'),
    path('customer/profile/edit/', views.customer_profile_edit_view, name='customer_profile_edit'),
    path('customer/profile/quick-update/', views.customer_profile_quick_update, name='customer_profile_quick_update'),
    path('customer/settings/toggle/', views.customer_settings_toggle, name='customer_settings_toggle'),
    path('customer/favorites/', views.customer_favorite_venues_view, name='customer_favorite_venues'),
    
    # Enhanced Booking Management URLs
    path('customer/booking/reschedule/', views.customer_booking_reschedule_request, name='customer_booking_reschedule'),
    
    # Favorite Venues AJAX URLs
    path('favorites/add/<int:venue_id>/', views.add_favorite_venue_view, name='add_favorite_venue'),
    path('favorites/remove/<int:venue_id>/', views.remove_favorite_venue_view, name='remove_favorite_venue'),
    path('favorites/check/<int:venue_id>/', views.check_favorite_status_view, name='check_favorite_status'),

    # Provider Dashboard URLs
    path('provider/', views.ProviderDashboardView.as_view(), name='provider_dashboard'),
    path('provider/todays-bookings/', views.provider_todays_bookings_view, name='provider_todays_bookings'),
    path('provider/earnings-reports/', views.provider_earnings_reports_view, name='provider_earnings_reports'),
    path('provider/earnings-export/', views.provider_earnings_export, name='provider_earnings_export'),
    path('provider/service-performance/', views.provider_service_performance_view, name='provider_service_performance'),
    path('provider/team-management/', views.provider_team_management_view, name='provider_team_management'),
    path('provider/venue-approval-submission/', views.provider_venue_approval_submission, name='provider_venue_approval_submission'),

    # Admin Dashboard URLs
    path('admin/', views.admin_dashboard, name='admin_dashboard'),
    path('admin/platform-overview/', views.admin_platform_overview, name='admin_platform_overview'),
    path('admin/user-statistics/', views.admin_user_statistics, name='admin_user_statistics'),
    path('admin/booking-analytics/', views.admin_booking_analytics, name='admin_booking_analytics'),
    path('admin/revenue-tracking/', views.admin_revenue_tracking, name='admin_revenue_tracking'),
    path('admin/revenue-export/', views.admin_revenue_export, name='admin_revenue_export'),
    path('admin/system-health/', views.admin_system_health, name='admin_system_health'),
]
