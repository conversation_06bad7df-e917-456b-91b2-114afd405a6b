# Generated by Django 5.2.3 on 2025-07-02 04:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0018_add_venue_creation_draft'),
    ]

    operations = [
        migrations.AddField(
            model_name='venuecreationdraft',
            name='amenities_data',
            field=models.JSONField(blank=True, default=list, help_text='Selected amenity types stored as JSON', verbose_name='amenities data'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='booking_policy',
            field=models.TextField(blank=True, help_text='Venue booking policy', max_length=1000, null=True, verbose_name='booking policy'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='cancellation_policy',
            field=models.TextField(blank=True, help_text='Venue cancellation policy', max_length=1000, null=True, verbose_name='cancellation policy'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='faqs_data',
            field=models.JSO<PERSON>ield(blank=True, default=list, help_text='Frequently asked questions stored as JSON', verbose_name='FAQs data'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='images_data',
            field=models.JSONField(blank=True, default=list, help_text='Image information and ordering stored as JSON', verbose_name='images data'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='latitude',
            field=models.DecimalField(blank=True, decimal_places=8, help_text='Latitude coordinate', max_digits=10, null=True, verbose_name='latitude'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='longitude',
            field=models.DecimalField(blank=True, decimal_places=8, help_text='Longitude coordinate', max_digits=11, null=True, verbose_name='longitude'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='operating_hours_data',
            field=models.JSONField(blank=True, default=dict, help_text='Operating hours for each day stored as JSON', verbose_name='operating hours data'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='services_data',
            field=models.JSONField(blank=True, default=list, help_text='Services with pricing and discount information stored as JSON', verbose_name='services data'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='special_instructions',
            field=models.TextField(blank=True, help_text='Special instructions for customers', max_length=500, null=True, verbose_name='special instructions'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='team_members_data',
            field=models.JSONField(blank=True, default=list, help_text='Team member information stored as JSON', verbose_name='team members data'),
        ),
        migrations.AddField(
            model_name='venuecreationdraft',
            name='zip_code',
            field=models.CharField(blank=True, help_text='Postal/ZIP code', max_length=10, null=True, verbose_name='zip code'),
        ),
    ]
