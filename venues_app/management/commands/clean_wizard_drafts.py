"""
Management command to clean up venue creation wizard drafts with old step names.
"""

from django.core.management.base import BaseCommand
from venues_app.models import VenueCreationDraft


class Command(BaseCommand):
    help = 'Clean up venue creation wizard drafts with old step names from 7-step wizard'

    def handle(self, *args, **options):
        """Clean up old draft data"""
        
        # Valid steps for the new 5-step wizard
        valid_steps = ['basic', 'location', 'services', 'gallery', 'details']
        
        # Old steps that should be removed
        old_steps = ['contact', 'hours_amenities', 'gallery_team', 'review_submit']
        
        updated_count = 0
        
        for draft in VenueCreationDraft.objects.all():
            original_steps = draft.completed_steps.copy() if draft.completed_steps else []
            
            # Filter out old step names
            cleaned_steps = [step for step in original_steps if step in valid_steps]
            
            # Update if there were changes
            if cleaned_steps != original_steps:
                draft.completed_steps = cleaned_steps
                
                # Also update current_step if it's an old step name
                if draft.current_step not in valid_steps:
                    # Map old steps to new steps
                    step_mapping = {
                        'contact': 'location',
                        'hours_amenities': 'details',
                        'gallery_team': 'gallery',
                        'review_submit': 'details'
                    }
                    draft.current_step = step_mapping.get(draft.current_step, 'basic')
                
                draft.save()
                updated_count += 1
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Updated draft for {draft.service_provider.business_name}: '
                        f'{original_steps} -> {cleaned_steps}'
                    )
                )
        
        if updated_count == 0:
            self.stdout.write(
                self.style.SUCCESS('No drafts needed cleaning. All drafts are up to date.')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully cleaned {updated_count} venue creation drafts.')
            )
