# --- Django Imports ---
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings
from django.utils.translation import gettext_lazy as _

# --- Python Standard Library ---
import csv
import os
from pathlib import Path
from decimal import Decimal, InvalidOperation

# --- Local App Imports ---
from venues_app.models import USCity


class Command(BaseCommand):
    """Management command to seed USCity data from us_cities.csv file."""
    
    help = 'Seed USCity model with data from us_cities.csv file'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing USCity data before seeding',
        )
        parser.add_argument(
            '--file-path',
            type=str,
            default='us_cities.csv',
            help='Path to the CSV file (default: us_cities.csv in project root)',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=1000,
            help='Number of records to process in each batch (default: 1000)',
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS('🌱 Starting USCity data seeding from CSV...')
        )
        
        # Validate CSV file exists
        csv_file_path = self.get_csv_file_path(options['file_path'])
        if not csv_file_path.exists():
            raise CommandError(f"CSV file not found: {csv_file_path}")
        
        # Clear existing data if requested
        if options['clear']:
            self.clear_existing_data()
        
        # Check if data already exists
        existing_count = USCity.objects.count()
        if existing_count > 0 and not options['clear']:
            self.stdout.write(
                self.style.WARNING(
                    f'⚠️  USCity table already contains {existing_count} records. '
                    'Use --clear to remove existing data first.'
                )
            )
            return
        
        # Process CSV file
        try:
            self.process_csv_file(csv_file_path, options['batch_size'])
        except Exception as e:
            raise CommandError(f"Error processing CSV file: {str(e)}")
        
        # Show final statistics
        self.show_final_statistics()
        
        self.stdout.write(
            self.style.SUCCESS('✅ USCity data seeding completed successfully!')
        )

    def get_csv_file_path(self, file_path):
        """Get the full path to the CSV file."""
        if os.path.isabs(file_path):
            return Path(file_path)
        else:
            # Look for file in project root
            project_root = Path(settings.BASE_DIR)
            return project_root / file_path

    def clear_existing_data(self):
        """Clear existing USCity data."""
        self.stdout.write('🗑️  Clearing existing USCity data...')
        deleted_count = USCity.objects.count()
        USCity.objects.all().delete()
        self.stdout.write(f'   ✅ Deleted {deleted_count} existing USCity records')

    def process_csv_file(self, csv_file_path, batch_size):
        """Process the CSV file and create USCity records."""
        self.stdout.write(f'📄 Processing CSV file: {csv_file_path}')
        
        created_count = 0
        error_count = 0
        batch_records = []
        
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            # Detect CSV dialect
            sample = csvfile.read(1024)
            csvfile.seek(0)
            sniffer = csv.Sniffer()
            dialect = sniffer.sniff(sample)
            
            reader = csv.DictReader(csvfile, dialect=dialect)
            
            # Validate required columns
            required_columns = ['ID', 'STATE_CODE', 'STATE_NAME', 'CITY', 'COUNTY', 'LATITUDE', 'LONGITUDE']
            if not all(col in reader.fieldnames for col in required_columns):
                missing_cols = [col for col in required_columns if col not in reader.fieldnames]
                raise CommandError(f"Missing required columns in CSV: {missing_cols}")
            
            self.stdout.write(f'📊 CSV columns found: {reader.fieldnames}')
            
            for row_num, row in enumerate(reader, start=2):  # Start at 2 because of header
                try:
                    # Create USCity record from CSV row
                    uscity_record = self.create_uscity_from_row(row, row_num)
                    if uscity_record:
                        batch_records.append(uscity_record)
                        
                        # Process batch when it reaches the specified size
                        if len(batch_records) >= batch_size:
                            created_batch = self.save_batch(batch_records)
                            created_count += created_batch
                            batch_records = []
                            
                            # Show progress
                            if created_count % (batch_size * 5) == 0:
                                self.stdout.write(f'   📈 Processed {created_count} records...')
                    
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  Error processing row {row_num}: {str(e)}')
                    )
                    
                    # Stop if too many errors
                    if error_count > 100:
                        raise CommandError("Too many errors encountered. Stopping.")
            
            # Process remaining records in the final batch
            if batch_records:
                created_batch = self.save_batch(batch_records)
                created_count += created_batch
        
        self.stdout.write(f'✅ Successfully created {created_count} USCity records')
        if error_count > 0:
            self.stdout.write(f'⚠️  {error_count} records had errors and were skipped')

    def create_uscity_from_row(self, row, row_num):
        """Create a USCity instance from a CSV row."""
        try:
            # Extract and clean data
            city_id = str(row['ID']).strip()
            state_code = str(row['STATE_CODE']).strip().upper()
            state_name = str(row['STATE_NAME']).strip()
            city = str(row['CITY']).strip()
            county = str(row['COUNTY']).strip()
            
            # Handle coordinates
            latitude = self.parse_decimal(row['LATITUDE'])
            longitude = self.parse_decimal(row['LONGITUDE'])
            
            # Validate required fields
            if not all([city_id, state_code, state_name, city, county]):
                raise ValueError("Missing required field data")
            
            # Create search vector for better search performance
            search_vector = f"{city} {county} {state_name} {state_code}".lower()
            
            return USCity(
                city_id=city_id,
                city=city,
                state_id=state_code,
                state_name=state_name,
                county_name=county,
                latitude=latitude,
                longitude=longitude,
                search_vector=search_vector
            )
            
        except Exception as e:
            raise ValueError(f"Invalid data in row {row_num}: {str(e)}")

    def parse_decimal(self, value):
        """Parse decimal value from string, handling empty values."""
        if not value or str(value).strip() == '':
            return None
        try:
            return Decimal(str(value).strip())
        except (InvalidOperation, ValueError):
            return None

    def save_batch(self, batch_records):
        """Save a batch of USCity records using bulk_create."""
        try:
            with transaction.atomic():
                USCity.objects.bulk_create(
                    batch_records,
                    ignore_conflicts=True,  # Skip duplicates
                    batch_size=500  # Smaller batch size for bulk_create
                )
                return len(batch_records)
        except Exception as e:
            # If bulk_create fails, try individual saves
            self.stdout.write(
                self.style.WARNING(f'⚠️  Bulk create failed, trying individual saves: {str(e)}')
            )
            return self.save_batch_individually(batch_records)

    def save_batch_individually(self, batch_records):
        """Save records individually if bulk_create fails."""
        saved_count = 0
        for record in batch_records:
            try:
                record.save()
                saved_count += 1
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  Failed to save record {record.city_id}: {str(e)}')
                )
        return saved_count

    def show_final_statistics(self):
        """Show final statistics after seeding."""
        total_count = USCity.objects.count()
        states_count = USCity.objects.values('state_name').distinct().count()
        counties_count = USCity.objects.values('county_name').distinct().count()
        
        self.stdout.write('\n📊 Final Statistics:')
        self.stdout.write(f'   🏙️  Total Cities: {total_count:,}')
        self.stdout.write(f'   🗺️  Total States: {states_count}')
        self.stdout.write(f'   🏞️  Total Counties: {counties_count:,}')
        
        # Show some sample data
        sample_cities = USCity.objects.select_related().order_by('?')[:3]
        if sample_cities:
            self.stdout.write('\n🎯 Sample Records:')
            for city in sample_cities:
                self.stdout.write(f'   • {city.city}, {city.county_name}, {city.state_name} ({city.state_id})')
